#include "workflow.h"
#include "api/api_manager.h"
#include "api/services/workflow_service.h"
#include "include/workflow.h"

#include "api/services/app_service.h"
#include "api/services/file_service.h"
#include "include/app.h"
#include "include/file.h"

namespace amssdk {
Workflow::Workflow(std::shared_ptr<ApiManager> api_manager)
    : api_manager_(api_manager) {}

ApiResult<WorkflowRunResponse> Workflow::WorkflowRun(
    const WorkflowRunRequest& request,
    const StreamEventCallback& stream_event_callback) const {
  return api_manager_->workflow().WorkflowRun(request, stream_event_callback);
}
ApiResult<WorkflowRunInfoResponse> Workflow::WorkflowRunInfo(
    const WorkflowRunInfoRequest& request) const {
  return api_manager_->workflow().WorkflowRunInfo(request);
}
ApiResult<SimpleResponse> Workflow::WorkflowTaskStop(
    const WorkflowTaskStopRequest& request) const {
  return api_manager_->workflow().WorkflowTaskStop(request);
}
ApiResult<WorkflowLogsResponse> Workflow::WorkflowLogs(
    const WorkflowLogsRequest& request) const {
  return api_manager_->workflow().WorkflowLogs(request);
}
ApiResult<FileResponse> Workflow::FileUpload(const FileRequest& request) const {
  return api_manager_->file().Upload(request.GetFilePath(), request.GetUser());
}
ApiResult<AppInfoResponse> Workflow::AppInfo(
    const AppInfoRequest& request) const {
  return api_manager_->app().Info(request);
}
ApiResult<AppParamResponse> Workflow::AppParameters(
    const AppParamRequest& request) const {
  return api_manager_->app().Parameters(request);
}
}  // namespace amssdk