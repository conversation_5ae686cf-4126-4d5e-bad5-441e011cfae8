#ifndef AMSSDK_AGENT_H
#define AMSSDK_AGENT_H

#include <memory>
#include <string>
#include "include/common/api_result.h"

namespace amssdk {
class StreamEvent;
using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;
class HttpClient;
class Authorization;

class AppService;
class AudioService;
class ConversationService;
class TaskService;
class FileService;
class ChatService;

class ChatRequest;
class FileRequest;
class TaskStopRequest;
class FeedbackRequest;
class SuggestedRequest;
class MessagesRequest;
class ConversationRequest;
class DeleteConversationRequest;
class RenameConversationRequest;
class AudioToTextRequest;
class AppMetaRequest;
class AppInfoRequest;
class AppParamRequest;

class FileResponse;
class ConversationResponse;
class SuggestedResponse;
class MessagesResponse;
class SimpleResponse;
class RenameConversationResponse;
class AudioToTextResponse;
class AppMetaResponse;
class AppInfoResponse;
class AppParamResponse;

class ApiManager;
}  // namespace amssdk

namespace amssdk {

class Agent {
 public:
  explicit Agent(std::shared_ptr<ApiManager> api_manager);
  LIBAMS_EXPORT ApiResult<void> SendChatMessage(
      const ChatRequest& request, const StreamEventCallback& callback) const;
  LIBAMS_EXPORT ApiResult<FileResponse> FileUpload(
      const FileRequest& request) const;
  LIBAMS_EXPORT ApiResult<SimpleResponse> StopTask(
      const TaskStopRequest& request) const;
  LIBAMS_EXPORT ApiResult<SimpleResponse> SendFeedback(
      const FeedbackRequest& request) const;
  LIBAMS_EXPORT ApiResult<SuggestedResponse> GetSuggested(
      const SuggestedRequest& request) const;
  LIBAMS_EXPORT ApiResult<MessagesResponse> GetMessages(
      const MessagesRequest& request) const;
  LIBAMS_EXPORT ApiResult<ConversationResponse> GetConversation(
      const ConversationRequest& request) const;
  LIBAMS_EXPORT ApiResult<SimpleResponse> DeleteConversation(
      const DeleteConversationRequest& request) const;
  LIBAMS_EXPORT ApiResult<RenameConversationResponse> RenameConversation(
      const RenameConversationRequest& request) const;
  LIBAMS_EXPORT ApiResult<AudioToTextResponse> AudioToText(
      const AudioToTextRequest& request) const;

  LIBAMS_EXPORT ApiResult<AppMetaResponse> AppMeta(
      const AppMetaRequest& request) const;
  LIBAMS_EXPORT ApiResult<AppInfoResponse> AppInfo(
      const AppInfoRequest& request) const;
  LIBAMS_EXPORT ApiResult<AppParamResponse> AppParameters(
      const AppParamRequest& request) const;

 private:
  std::shared_ptr<ApiManager> api_manager_;
};
}  // namespace amssdk

#endif  //AMSSDK_AGENT_H
