#include "agent.h"

#include "api/api_manager.h"
#include "api/services/app_service.h"
#include "api/services/audio_service.h"
#include "api/services/chat_service.h"
#include "api/services/conversation_service.h"
#include "api/services/file_service.h"
#include "api/services/task_service.h"
#include "include/request.h"
#include "include/response.h"

namespace amssdk {
Agent::Agent(std::shared_ptr<ApiManager> api_manager)
    : api_manager_(api_manager) {}

ApiResult<void> Agent::SendChatMessage(
    const ChatRequest& request, const StreamEventCallback& callback) const {
  return api_manager_->chat().SendChatMessage(request, callback);
}

ApiResult<FileResponse> Agent::FileUpload(const FileRequest& request) const {
  return api_manager_->file().Upload(request.GetFilePath(), request.GetUser());
}

ApiResult<SimpleResponse> Agent::StopTask(
    const TaskStopRequest& request) const {
  return api_manager_->task().StopTask(request);
}

ApiResult<SimpleResponse> Agent::SendFeedback(
    const FeedbackRequest& request) const {
  return api_manager_->task().SendFeedback(request);
}

ApiResult<SuggestedResponse> Agent::GetSuggested(
    const SuggestedRequest& request) const {
  return api_manager_->conversation().GetSuggested(request);
}

ApiResult<MessagesResponse> Agent::GetMessages(
    const MessagesRequest& request) const {
  return api_manager_->conversation().GetMessages(request);
}

ApiResult<ConversationResponse> Agent::GetConversation(
    const ConversationRequest& request) const {
  return api_manager_->conversation().GetConversation(request);
}

ApiResult<SimpleResponse> Agent::DeleteConversation(
    const DeleteConversationRequest& request) const {
  return api_manager_->conversation().DeleteConversation(request);
}

ApiResult<RenameConversationResponse> Agent::RenameConversation(
    const RenameConversationRequest& request) const {
  return api_manager_->conversation().RenameConversation(request);
}

ApiResult<AudioToTextResponse> Agent::AudioToText(
    const AudioToTextRequest& request) const {
  return api_manager_->audio().AudioToText(request);
}

ApiResult<AppMetaResponse> Agent::AppMeta(const AppMetaRequest& request) const {
  return api_manager_->app().Meta(request);
}

ApiResult<AppInfoResponse> Agent::AppInfo(const AppInfoRequest& request) const {
  return api_manager_->app().Info(request);
}
ApiResult<AppParamResponse> Agent::AppParameters(
    const AppParamRequest& request) const {
  return api_manager_->app().Parameters(request);
}
}  // namespace amssdk