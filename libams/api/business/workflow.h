#ifndef AMSSDK_WORKFLOW_H
#define AMSSDK_WORKFLOW_H
#include <memory>

#include "include/common/api_result.h"

namespace amssdk {
class StreamEvent;
using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;
class ApiManager;

class FileRequest;
class AppInfoRequest;
class AppParamRequest;
class WorkflowRunRequest;
class WorkflowRunInfoRequest;
class WorkflowTaskStopRequest;
class WorkflowLogsRequest;

class FileResponse;
class AppInfoResponse;
class AppParamResponse;
class WorkflowLogsResponse;
class WorkflowRunResponse;
class WorkflowRunInfoResponse;
class SimpleResponse;

}  // namespace amssdk
namespace amssdk {
class Workflow {
 public:
  explicit Workflow(std::shared_ptr<ApiManager> api_manager);

  LIBAMS_EXPORT ApiResult<WorkflowRunResponse> WorkflowRun(
      const WorkflowRunRequest& request,
      const StreamEventCallback& stream_event_callback) const;

  LIBAMS_EXPORT ApiResult<WorkflowRunInfoResponse> WorkflowRunInfo(
      const WorkflowRunInfoRequest& request) const;

  LIBAMS_EXPORT ApiResult<SimpleResponse> WorkflowTaskStop(
      const WorkflowTaskStopRequest& request) const;
  LIBAMS_EXPORT ApiResult<WorkflowLogsResponse> WorkflowLogs(
      const WorkflowLogsRequest& request) const;

  LIBAMS_EXPORT ApiResult<FileResponse> FileUpload(
      const FileRequest& request) const;

  LIBAMS_EXPORT ApiResult<AppInfoResponse> AppInfo(
      const AppInfoRequest& request) const;

  LIBAMS_EXPORT ApiResult<AppParamResponse> AppParameters(
      const AppParamRequest& request) const;

 private:
  std::shared_ptr<ApiManager> api_manager_;
};

}  // namespace amssdk
#endif  //AMSSDK_WORKFLOW_H
