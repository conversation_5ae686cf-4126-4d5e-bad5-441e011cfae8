#ifndef AMSSDK_AMS_CLIENT_V2_H
#define AMSSDK_AMS_CLIENT_V2_H

#include <functional>
#include <memory>
#include <string>

#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "network/authorization.h"
#include "network/http_client.h"

namespace amssdk {

// 前置声明
class ChatService;
class WorkflowService;
class KnowledgeService;
class ConversationService;
class FileService;
class AudioService;
class AppService;
class TaskService;

class AgentAPI;
class ChatflowAPI;
class ChatAPI;
class KnowledgeAPI;
class ConversationAPI;
class FileAPI;
class AppAPI;

class AmsClient {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT explicit AmsClient(const std::string& base_url);
  LIBAMS_EXPORT ~AmsClient();

  // 配置方法 - 直接管理HttpClient和Authorization
  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key);
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms);
  LIBAMS_EXPORT void SetBaseUrl(const std::string& base_url);

  // 业务功能分组 - 推荐使用方式
  LIBAMS_EXPORT AgentAPI& agent() const;
  LIBAMS_EXPORT ChatflowAPI& chatflow() const;
  LIBAMS_EXPORT ChatAPI& chat() const;
  LIBAMS_EXPORT KnowledgeAPI& knowledge() const;
  LIBAMS_EXPORT ConversationAPI& conversation() const;
  LIBAMS_EXPORT FileAPI& file() const;
  LIBAMS_EXPORT AppAPI& app() const;

  // 直接访问Service（高级用户）
  LIBAMS_EXPORT ChatService& GetChatService() const;
  LIBAMS_EXPORT WorkflowService& GetWorkflowService() const;
  LIBAMS_EXPORT KnowledgeService& GetKnowledgeService() const;
  LIBAMS_EXPORT ConversationService& GetConversationService() const;
  LIBAMS_EXPORT FileService& GetFileService() const;
  LIBAMS_EXPORT AudioService& GetAudioService() const;
  LIBAMS_EXPORT AppService& GetAppService() const;
  LIBAMS_EXPORT TaskService& GetTaskService() const;

  // 内部访问方法（供Service和BusinessAPI使用）
  HttpClient& GetHttpClient() { return http_client_; }
  Authorization& GetAuthorization() { return auth_; }

 private:
  // 网络和认证管理
  HttpClient http_client_;
  Authorization& auth_;

  // Service实例（延迟初始化）
  mutable std::unique_ptr<ChatService> chat_service_;
  mutable std::unique_ptr<WorkflowService> workflow_service_;
  mutable std::unique_ptr<KnowledgeService> knowledge_service_;
  mutable std::unique_ptr<ConversationService> conversation_service_;
  mutable std::unique_ptr<FileService> file_service_;
  mutable std::unique_ptr<AudioService> audio_service_;
  mutable std::unique_ptr<AppService> app_service_;
  mutable std::unique_ptr<TaskService> task_service_;

  // 业务API实例（延迟初始化）
  mutable std::unique_ptr<AgentAPI> agent_api_;
  mutable std::unique_ptr<ChatflowAPI> chatflow_api_;
  mutable std::unique_ptr<ChatAPI> chat_api_;
  mutable std::unique_ptr<KnowledgeAPI> knowledge_api_;
  mutable std::unique_ptr<ConversationAPI> conversation_api_;
  mutable std::unique_ptr<FileAPI> file_api_;
  mutable std::unique_ptr<AppAPI> app_api_;
};

}  // namespace amssdk

#endif  // AMSSDK_AMS_CLIENT_V2_H
