#include "ams_client_v2.h"

#include "api/services/chat_service.h"
#include "api/services/workflow_service.h"
#include "api/services/knowledge_service.h"
#include "api/services/conversation_service.h"
#include "api/services/file_service.h"
#include "api/services/audio_service.h"
#include "api/services/app_service.h"
#include "api/services/task_service.h"

#include "api/business/agent_api.h"
#include "api/business/chatflow_api.h"
#include "api/business/chat_api.h"

namespace amssdk {

AmsClient::AmsClient(const std::string& base_url)
    : http_client_(), auth_(Authorization::Authorizer()) {
  if (!base_url.empty()) {
    http_client_.SetBaseUrl(base_url);
  }
}

AmsClient::~AmsClient() = default;

// 配置方法
bool AmsClient::SetAuthorizationKey(const std::string& key) {
  return auth_.SetKey(key);
}

void AmsClient::SetMaxTimeout(int32_t ms) {
  auth_.SetMaxTimeout(ms);
}

void AmsClient::SetBaseUrl(const std::string& base_url) {
  http_client_.SetBaseUrl(base_url);
}

// 业务API访问方法（延迟初始化）
AgentAPI& AmsClient::agent() const {
  if (!agent_api_) {
    agent_api_ = std::make_unique<AgentAPI>(*this);
  }
  return *agent_api_;
}

ChatflowAPI& AmsClient::chatflow() const {
  if (!chatflow_api_) {
    chatflow_api_ = std::make_unique<ChatflowAPI>(*this);
  }
  return *chatflow_api_;
}

ChatAPI& AmsClient::chat() const {
  if (!chat_api_) {
    chat_api_ = std::make_unique<ChatAPI>(*this);
  }
  return *chat_api_;
}

// Service访问方法（延迟初始化）
ChatService& AmsClient::GetChatService() const {
  if (!chat_service_) {
    chat_service_ = std::make_unique<ChatService>(
        const_cast<HttpClient&>(http_client_), 
        const_cast<Authorization&>(auth_));
  }
  return *chat_service_;
}

WorkflowService& AmsClient::GetWorkflowService() const {
  if (!workflow_service_) {
    workflow_service_ = std::make_unique<WorkflowService>(
        const_cast<HttpClient&>(http_client_), 
        const_cast<Authorization&>(auth_));
  }
  return *workflow_service_;
}

KnowledgeService& AmsClient::GetKnowledgeService() const {
  if (!knowledge_service_) {
    knowledge_service_ = std::make_unique<KnowledgeService>(
        const_cast<HttpClient&>(http_client_), 
        const_cast<Authorization&>(auth_));
  }
  return *knowledge_service_;
}

ConversationService& AmsClient::GetConversationService() const {
  if (!conversation_service_) {
    conversation_service_ = std::make_unique<ConversationService>(
        const_cast<HttpClient&>(http_client_), 
        const_cast<Authorization&>(auth_));
  }
  return *conversation_service_;
}

// 其他Service的实现类似...

}  // namespace amssdk
