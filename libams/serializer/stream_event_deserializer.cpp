#include "stream_event_deserializer.h"
#include <algorithm>
#include <regex>
#include <sstream>
#include "include/common/common.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {

std::string Trim(const std::string& str) {
  size_t start = str.find_first_not_of(" \t\n\r");
  if (start == std::string::npos)
    return "";

  size_t end = str.find_last_not_of(" \t\n\r");
  return str.substr(start, end - start + 1);
}

// Helper to extract JSON from SSE data line
bool ExtractJsonFromSseLine(const std::string& line, std::string& json) {
  const std::string data_prefix = "data:";
  size_t pos = line.find(data_prefix);
  if (pos == std::string::npos)
    return false;

  json = line.substr(pos + data_prefix.length());
  json = Trim(json);

  // Handle multi-line JSON or incomplete JSON
  if (json.empty())
    return false;

  return true;
}

}  // anonymous namespace

std::unique_ptr<StreamEvent> StreamEventDeserializer::DeserializeStreamEvent(
    const std::string& json_str) {
  try {
    stats_.total_events_parsed++;

    if (json_str.empty()) {
      SetError("Empty input string");
      return nullptr;
    }

    std::string json;

    // Try to extract JSON from SSE format
    if (ExtractJsonFromSseLine(json_str, json)) {
      stats_.sse_events_parsed++;
    } else if (json_str.front() == '{' && json_str.back() == '}') {
      // Direct JSON format
      json = json_str;
      stats_.direct_json_parsed++;
    } else {
      SetError("Invalid format: not SSE or direct JSON");
      stats_.failed_parses++;
      return nullptr;
    }

    // Parse the JSON
    nlohmann::json json_object;
    try {
      json_object = nlohmann::json::parse(json);
    } catch (const nlohmann::json::parse_error& e) {
      SetError("JSON parse error: " + std::string(e.what()));
      stats_.failed_parses++;
      return nullptr;
    }

    // Extract event type
    std::string event_type;
    if (!SafeGetJsonValue(json_object, "event", event_type)) {
      SetError("Missing 'event' field in JSON");
      stats_.failed_parses++;
      return nullptr;
    }

    // Dispatch to appropriate parser based on event type
    std::unique_ptr<StreamEvent> result = nullptr;

    if (event_type == "message" || event_type == "agent_message") {
      if (ValidateMessageEvent(json_object)) {
        result = ParseMessageEvent(json_object);
      }
    } else if (event_type == "agent_thought") {
      result = ParseAgentThoughtEvent(json_object);
    } else if (event_type == "message_end") {
      result = ParseMessageEndEvent(json_object);
    } else if (event_type == "message_file") {
      result = ParseMessageFileEvent(json_object);
    } else if (event_type == "tts_message") {
      result = ParseTtsMessageEvent(json_object);
    } else if (event_type == "tts_message_end") {
      result = ParseTtsMessageEndEvent(json_object);
    } else if (event_type == "message_replace") {
      result = ParseMessageReplaceEvent(json_object);
    } else if (event_type == "workflow_started") {
      result =
          ParseWorkflowEvent(json_object, StreamEvent::Type::WORKFLOW_STARTED);
    } else if (event_type == "node_started") {
      result = ParseWorkflowEvent(json_object, StreamEvent::Type::NODE_STARTED);
    } else if (event_type == "node_finished") {
      result =
          ParseWorkflowEvent(json_object, StreamEvent::Type::NODE_FINISHED);
    } else if (event_type == "workflow_finished") {
      result =
          ParseWorkflowEvent(json_object, StreamEvent::Type::WORKFLOW_FINISHED);
    } else if (event_type == "error") {
      result = ParseErrorEvent(json_object);
    } else {
      SetError("Unknown event type: " + event_type);
    }

    if (result) {
      stats_.successful_parses++;
      last_error_.clear();
    } else {
      stats_.failed_parses++;
    }

    return result;

  } catch (const std::exception& e) {
    SetError("Unexpected error: " + std::string(e.what()));
    stats_.failed_parses++;
    return nullptr;
  }
}

std::vector<std::unique_ptr<StreamEvent>>
StreamEventDeserializer::DeserializeMultipleEvents(const std::string& chunk) {
  std::vector<std::unique_ptr<StreamEvent>> events;

  if (chunk.empty()) {
    return events;
  }

  std::istringstream stream(chunk);
  std::string line;
  std::string current_data;

  while (std::getline(stream, line)) {
    // Handle different line endings
    if (!line.empty() && line.back() == '\r') {
      line.pop_back();
    }

    // Skip empty lines
    if (line.empty()) {
      if (!current_data.empty()) {
        // Process accumulated data
        auto event = DeserializeStreamEvent(current_data);
        if (event) {
          events.push_back(std::move(event));
        }
        current_data.clear();
      }
      continue;
    }

    // Check if this is a data line
    std::string potential_json;
    if (ExtractJsonFromSseLine(line, potential_json)) {
      current_data = line;  // Store the entire line for processing
    } else if (line.find("data:") == 0) {
      // Accumulate multi-line data
      current_data += line + "\n";
    }
  }

  // Process any remaining data
  if (!current_data.empty()) {
    auto event = DeserializeStreamEvent(current_data);
    if (event) {
      events.push_back(std::move(event));
    }
  }

  return events;
}

bool StreamEventDeserializer::ExtractJsonFromSSE(const std::string& sse_line,
                                                 std::string& json) {
  return ExtractJsonFromSseLine(sse_line, json);
}

bool StreamEventDeserializer::ValidateMessageEvent(const nlohmann::json& j) {
  // Check required fields for message events
  bool has_task_id = j.contains("task_id") && !j["task_id"].is_null();
  bool has_message_id = j.contains("message_id") && !j["message_id"].is_null();
  bool has_answer = j.contains("answer") && !j["answer"].is_null();

  if (!has_task_id || !has_message_id || !has_answer) {
    SetError("Message event missing required fields");
    return false;
  }

  return true;
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseMessageEvent(
    const nlohmann::json& j) {
  std::string task_id, message_id, conversation_id, answer;

  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "message_id", message_id);
  SafeGetJsonValue(j, "conversation_id", conversation_id);
  SafeGetJsonValue(j, "answer", answer);

  return std::make_unique<MessageEvent>(
      std::move(task_id), std::move(message_id), std::move(conversation_id),
      std::move(answer));
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseAgentThoughtEvent(
    const nlohmann::json& j) {
  std::string id, task_id, message_id, conversation_id, observation, thought,
      tool, tool_input;
  int position = 0;
  std::vector<std::string> message_files;

  SafeGetJsonValue(j, "id", id);
  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "message_id", message_id);
  SafeGetJsonValue(j, "conversation_id", conversation_id);
  SafeGetJsonValue(j, "observation", observation);
  SafeGetJsonValue(j, "position", position, 0);
  SafeGetJsonValue(j, "thought", thought);
  SafeGetJsonValue(j, "tool", tool);
  SafeGetJsonValue(j, "tool_input", tool_input);

  if (j.contains("message_files") && j["message_files"].is_array()) {
    for (const auto& file : j["message_files"]) {
      if (file.is_string()) {
        message_files.push_back(file.get<std::string>());
      }
    }
  }

  return std::make_unique<AgentThoughtEvent>(
      std::move(id), std::move(task_id), std::move(message_id),
      std::move(conversation_id), std::move(observation), position,
      std::move(thought), std::move(tool), std::move(tool_input),
      std::move(message_files));
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseMessageEndEvent(
    const nlohmann::json& j) {
  std::string task_id, message_id, conversation_id;
  MetaData metadata;

  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "message_id", message_id);
  SafeGetJsonValue(j, "conversation_id", conversation_id);

  // TODO: Implement metadata parsing when deserializer is available
  // For now, use empty metadata

  return std::make_unique<MessageEndEvent>(
      std::move(task_id), std::move(message_id), std::move(conversation_id),
      metadata);
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseMessageFileEvent(
    const nlohmann::json& j) {
  std::string id, belongs_to, conversation_id, url;
  FileAttachment::FileType file_type = FileAttachment::FileType::DOCUMENT;

  SafeGetJsonValue(j, "id", id);
  SafeGetJsonValue(j, "belongs_to", belongs_to);
  SafeGetJsonValue(j, "conversation_id", conversation_id);
  SafeGetJsonValue(j, "url", url);

  std::string file_type_str;
  if (SafeGetJsonValue(j, "file_type", file_type_str)) {
    file_type = SerializerUtils::FileTypeFromString(file_type_str);
  }

  return std::make_unique<MessageFileEvent>(
      std::move(id), std::move(belongs_to), std::move(conversation_id),
      file_type, std::move(url));
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseTtsMessageEvent(
    const nlohmann::json& j) {
  std::string task_id, message_id, audio;
  int created_at = 0;

  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "message_id", message_id);
  SafeGetJsonValue(j, "audio", audio);
  SafeGetJsonValue(j, "created_at", created_at, 0);

  return std::make_unique<TtsMessageEvent>(
      std::move(task_id), std::move(message_id), std::move(audio), created_at);
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseTtsMessageEndEvent(
    const nlohmann::json& j) {
  std::string task_id, message_id, audio;
  int created_at = 0;

  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "message_id", message_id);
  SafeGetJsonValue(j, "audio", audio);
  SafeGetJsonValue(j, "created_at", created_at, 0);

  return std::make_unique<TtsMessageEndEvent>(
      std::move(task_id), std::move(message_id), std::move(audio), created_at);
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseMessageReplaceEvent(
    const nlohmann::json& j) {
  std::string task_id, message_id, conversation_id, answer;
  int created_at = 0;

  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "message_id", message_id);
  SafeGetJsonValue(j, "conversation_id", conversation_id);
  SafeGetJsonValue(j, "answer", answer);
  SafeGetJsonValue(j, "created_at", created_at, 0);

  return std::make_unique<MessageReplaceEvent>(
      std::move(task_id), std::move(message_id), std::move(conversation_id),
      std::move(answer), created_at);
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseErrorEvent(
    const nlohmann::json& j) {
  std::string task_id, message_id, code, message;
  int status = 0;

  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "message_id", message_id);
  SafeGetJsonValue(j, "status", status, 0);
  SafeGetJsonValue(j, "code", code);
  SafeGetJsonValue(j, "message", message);

  return std::make_unique<ErrorEvent>(std::move(task_id), std::move(message_id),
                                      status, std::move(code),
                                      std::move(message));
}

std::unique_ptr<StreamEvent> StreamEventDeserializer::ParseWorkflowEvent(
    const nlohmann::json& j, StreamEvent::Type type) {
  std::string task_id, workflow_run_id, data;

  SafeGetJsonValue(j, "task_id", task_id);
  SafeGetJsonValue(j, "workflow_run_id", workflow_run_id);

  if (j.contains("data")) {
    data = j["data"].dump();
  }

  return std::make_unique<WorkflowEvent>(
      std::move(task_id), std::move(workflow_run_id), std::move(data), type);
}

void StreamEventDeserializer::SetError(const std::string& error) {
  last_error_ = error;
}

}  // namespace amssdk